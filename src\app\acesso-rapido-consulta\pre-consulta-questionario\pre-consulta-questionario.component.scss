// Variáveis de cores
$primary-color: #667eea;
$secondary-color: #764ba2;
$accent-color: #f093fb;
$success-color: #4facfe;
$background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$ai-color: #667eea;
$text-dark: #2d3748;
$text-light: #718096;
$white: #ffffff;
$shadow: 0 0.625rem 1.5625rem rgba(0, 0, 0, 0.1);

// Enhanced Design System Variables for Modal
$modal-primary: #667eea;
$modal-secondary: #764ba2;
$modal-accent: #4facfe;
$modal-success: #10b981;
$modal-warning: #f59e0b;
$modal-error: #ef4444;
$modal-info: #3b82f6;

// Semantic color tokens
$surface-primary: #ffffff;
$surface-secondary: #f8fafc;
$surface-tertiary: #f1f5f9;
$surface-elevated: #ffffff;
$surface-overlay: rgba(15, 23, 42, 0.8);

// Text hierarchy
$text-primary: #0f172a;
$text-secondary: #475569;
$text-tertiary: #64748b;
$text-inverse: #ffffff;
$text-accent: $modal-primary;

// Responsive spacing system
$space-xs: 0.25rem;   // 4px
$space-sm: 0.5rem;    // 8px
$space-md: 1rem;      // 16px
$space-lg: 1.5rem;    // 24px
$space-xl: 2rem;      // 32px
$space-2xl: 3rem;     // 48px
$space-3xl: 4rem;     // 64px

// Responsive font sizes
$font-xs: 0.75rem;    // 12px
$font-sm: 0.875rem;   // 14px
$font-base: 1rem;     // 16px
$font-lg: 1.125rem;   // 18px
$font-xl: 1.25rem;    // 20px
$font-2xl: 1.5rem;    // 24px
$font-3xl: 1.875rem;  // 30px
$font-4xl: 2.25rem;   // 36px

// Responsive breakpoints
$breakpoint-xs: 480px;
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;

// Additional variables for modal system
// Border radius scale
$radius-sm: 0.375rem; // 6px
$radius-md: 0.5rem; // 8px
$radius-lg: 0.75rem; // 12px
$radius-xl: 1rem; // 16px
$radius-2xl: 1.5rem; // 24px
$radius-full: 9999px;

// Shadow system
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md:
  0 4px 6px -1px rgba(0, 0, 0, 0.1),
  0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg:
  0 10px 15px -3px rgba(0, 0, 0, 0.1),
  0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl:
  0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

// Typography scale (additional)
$text-xs: 0.75rem; // 12px
$text-sm: 0.875rem; // 14px
$text-base: 1rem; // 16px
$text-lg: 1.125rem; // 18px
$text-xl: 1.25rem; // 20px
$text-2xl: 1.5rem; // 24px
$text-3xl: 1.875rem; // 30px

// Z-index scale
$z-modal: 1000;
$z-overlay: 999;
$z-dropdown: 50;

.ai-questionnaire-container {
  min-height: 100vh;
  background: $background;
  font-family:
    "Inter",
    -apple-system,
    BlinkMacSystemFont,
    sans-serif;
  font-size: clamp(1rem, 2.5vw, 1.3rem);
  position: relative;
  overflow-x: hidden;
}

/* #region Tela Ociosa */
.idle-screen {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: clamp($space-md, 4vw, $space-xl);
}

.idle-content {
  text-align: center;
  background: rgba($white, 0.95);
  padding: clamp($space-2xl, 8vw, 3.75rem) clamp($space-lg, 6vw, 2.5rem);
  border-radius: clamp(1.25rem, 4vw, 1.875rem);
  box-shadow: 0 1.25rem 2.5rem rgba(0, 0, 0, 0.1);
  max-width: min(90vw, 34.375rem);
  width: 100%;
  backdrop-filter: blur(0.625rem);
  border: 1px solid rgba($white, 0.2);

  h1 {
    color: $text-dark;
    font-size: clamp($font-2xl, 5vw, $font-4xl);
    font-weight: 700;
    margin: clamp($space-lg, 4vw, $space-xl) 0 clamp($space-sm, 2vw, $space-md) 0;
  }

  p {
    color: $text-light;
    font-size: clamp($font-base, 3vw, $font-xl);
    margin-bottom: clamp($space-lg, 5vw, 2.5rem);
    line-height: 1.6;
  }
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: clamp($space-sm, 2vw, $space-md);
  align-items: center;
  width: 100%;
}

/* IA Robot CSS */
.ai-robot {
  display: inline-block;
  animation: float 3s ease-in-out infinite;
  transform: scale(1.1);

  @media (max-width: $breakpoint-md) {
    transform: scale(1);
  }

  @media (max-width: $breakpoint-xs) {
    transform: scale(0.9);
  }
}

.robot-head {
  width: clamp(3.5rem, 8vw, 5rem);
  height: clamp(3.5rem, 8vw, 5rem);
  background: linear-gradient(135deg, $primary-color, $secondary-color);
  border-radius: clamp(1rem, 3vw, 1.25rem);
  position: relative;
  margin: 0 auto clamp($space-sm, 2vw, $space-md);
  box-shadow: 0 0.5rem 1.25rem rgba($primary-color, 0.3);
}

.robot-eyes {
  display: flex;
  justify-content: space-between;
  padding: clamp($space-md, 4vw, 1.25rem) clamp($space-sm, 3vw, $space-md) 0;
}

.eye {
  width: clamp(0.5rem, 1.5vw, 0.75rem);
  height: clamp(0.5rem, 1.5vw, 0.75rem);
  background: $white;
  border-radius: 50%;
  animation: blink 3s infinite;

  &.left-eye {
    animation-delay: 0.1s;
  }

  &.right-eye {
    animation-delay: 0.2s;
  }
}

.robot-mouth {
  width: clamp(1rem, 2.5vw, 1.25rem);
  height: clamp(0.4rem, 1vw, 0.5rem);
  background: $white;
  border-radius: 0 0 0.625rem 0.625rem;
  margin: clamp(0.4rem, 1vw, 0.5rem) auto 0;
}

.robot-body {
  width: clamp(2.5rem, 6vw, 3.75rem);
  height: clamp(1.5rem, 4vw, 2.5rem);
  background: linear-gradient(135deg, $primary-color, $secondary-color);
  border-radius: clamp(0.75rem, 2vw, $space-md);
  margin: 0 auto;
  position: relative;
}

.robot-chest {
  width: clamp(0.4rem, 1vw, 0.5rem);
  height: clamp(0.4rem, 1vw, 0.5rem);
  background: $white;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation: pulse 2s infinite;
}

/* Start Button */
.start-btn {
  background: linear-gradient(135deg, $success-color, $accent-color);
  border: none;
  padding: clamp($space-md, 3vw, 1.25rem) clamp($space-xl, 5vw, 2.8125rem);
  border-radius: 3.125rem;
  color: $white;
  font-size: clamp($font-lg, 3vw, $font-2xl);
  font-weight: 700;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 0.5rem 1.5625rem rgba($success-color, 0.3);

  &:hover {
    transform: translateY(-0.125rem);
    box-shadow: 0 0.75rem 2.1875rem rgba($success-color, 0.4);
  }

  span {
    position: relative;
    z-index: 2;
  }
}

.btn-glow {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba($white, 0.3), transparent);
  transition: left 0.5s;
}

.start-btn:hover .btn-glow {
  left: 100%;
}
/* #endregion */

/* #region Chat Interface */
.chat-interface {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow-x: hidden;
}

/* Controls Header */
.controls-header {
  position: absolute;
  top: clamp($space-md, 3vw, $space-xl);
  left: clamp($space-md, 3vw, $space-xl);
  z-index: 100;
  display: flex;
  align-items: center;
  gap: clamp($space-sm, 2vw, $space-md);
  background: rgba($white, 0.95);
  padding: clamp($space-sm, 2vw, 0.75rem) clamp($space-md, 3vw, 1.25rem);
  border-radius: clamp($space-lg, 4vw, 1.5625rem);
  box-shadow: $shadow;
  backdrop-filter: blur(0.625rem);
}

.mode-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: clamp(2rem, 5vw, 2.5rem);
  height: clamp(2rem, 5vw, 2.5rem);
  background: linear-gradient(135deg, $primary-color, $secondary-color);
  border-radius: 50%;
  color: $white;

  .mode-icon {
    font-size: clamp($font-base, 2.5vw, 1.25rem);
  }
}

.mode-toggle {
  font-weight: 500;
  color: $text-dark;
  font-size: clamp($font-sm, 2vw, $font-base);
}

/* Main Chat Area */
.main-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  gap: clamp($space-lg, 4vw, $space-2xl);
  padding: clamp(6rem, 12vw, 7.5rem) clamp($space-md, 4vw, $space-2xl) clamp($space-lg, 4vw, $space-2xl);
  max-width: min(95vw, 85rem);
  margin: 0 auto;
  width: 100%;
  min-height: 0; // Permite que o flex funcione corretamente
}

/* Response and Data Section Layout */
.response-data-section {
  display: flex;
  flex-direction: column;
  gap: clamp($space-lg, 4vw, $space-2xl);
  width: 100%;
  align-items: center;
  max-width: 100%;

  @media (min-width: $breakpoint-lg) {
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
  }

  @media (min-width: $breakpoint-xl) {
    gap: clamp($space-xl, 3vw, $space-3xl);
  }
}

/* AI Section */
.ai-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: clamp($space-md, 3vw, $space-xl);
}

.ai-avatar {
  width: clamp(5rem, 12vw, 8.25rem);
  height: clamp(5rem, 12vw, 8.25rem);
  background: $white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: $shadow;
  transition: all 0.3s ease;

  &.processing {
    animation: processing-pulse 2s infinite;
    box-shadow: 0 0 clamp(1rem, 3vw, 1.875rem) rgba($primary-color, 0.5);
  }

  &.listening {
    animation: listening-pulse 1s infinite;
    box-shadow: 0 0 clamp(1rem, 3vw, 1.875rem) rgba($accent-color, 0.5);
  }

  &.waiting {
    animation: waiting-pulse 2s infinite;
    box-shadow: 0 0 clamp(1rem, 3vw, 1.875rem) rgba($success-color, 0.5);
  }
}

.ai-face {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: clamp(0.3rem, 1vw, 0.5rem);
}

.ai-eyes {
  display: flex;
  gap: clamp(0.5rem, 1.5vw, 0.75rem);

  .eye {
    width: clamp(0.4rem, 1vw, 0.5rem);
    height: clamp(0.4rem, 1vw, 0.5rem);
    background: $primary-color;
    border-radius: 50%;
    animation: ai-blink 4s infinite;
  }
}

.ai-mouth {
  width: clamp(0.75rem, 2vw, 1rem);
  height: clamp(0.3rem, 0.8vw, 0.375rem);
  background: $primary-color;
  border-radius: 0 0 0.5rem 0.5rem;
  transition: all 0.3s ease;

  &.talking {
    animation: mouth-talk 0.5s infinite alternate;
  }
}

.ai-pulse {
  position: absolute;
  top: clamp(-0.5rem, -1.5vw, -0.625rem);
  left: clamp(-0.5rem, -1.5vw, -0.625rem);
  right: clamp(-0.5rem, -1.5vw, -0.625rem);
  bottom: clamp(-0.5rem, -1.5vw, -0.625rem);
  border: clamp(2px, 0.5vw, 3px) solid $primary-color;
  border-radius: 50%;
  animation: pulse-ring 2s infinite;
}

/* Response Section */
.response-section {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: min(90vw, 43.75rem);
  flex: 1;

  @media (min-width: $breakpoint-lg) {
    align-items: flex-start;
    max-width: none;
  }
}

.ai-message {
  max-width: 100%;
  margin-bottom: clamp($space-md, 3vw, $space-xl);
  animation: fadeIn 0.5s ease-in-out;
}

.message-bubble {
  background: $white;
  padding: clamp($space-lg, 4vw, 1.875rem) clamp($space-xl, 5vw, 1.875rem);
  border-radius: clamp($space-lg, 4vw, 1.5625rem);
  box-shadow: $shadow;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    left: clamp($space-md, 3vw, 1.25rem);
    bottom: clamp(-0.5rem, -1vw, -0.625rem);
    width: 0;
    height: 0;
    border-left: clamp(0.5rem, 1.5vw, 0.625rem) solid transparent;
    border-right: clamp(0.5rem, 1.5vw, 0.625rem) solid transparent;
    border-top: clamp(0.5rem, 1.5vw, 0.625rem) solid $white;
  }

  p {
    margin: 0;
    color: $text-dark;
    font-size: clamp($font-base, 3vw, $font-xl);
    line-height: 1.6;
    font-weight: 500;
  }
}

.processing-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: clamp($space-sm, 2vw, $space-md);
  color: $text-light;
  padding: clamp($space-md, 3vw, $space-lg);
  
  .processing-text {
    font-size: clamp($font-sm, 2.5vw, $font-base);
    font-weight: 500;
    color: $text-light;
    margin-top: clamp($space-xs, 1vw, $space-sm);
  }
}

.typing-dots {
  display: flex;
  gap: clamp(0.2rem, 0.5vw, 0.25rem);

  span {
    width: clamp(0.4rem, 1vw, 0.5rem);
    height: clamp(0.4rem, 1vw, 0.5rem);
    background: #fff;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: 0s;
    }
    &:nth-child(2) {
      animation-delay: 0.2s;
    }
    &:nth-child(3) {
      animation-delay: 0.4s;
    }
  }
}
/* #endregion */

/* #region Data Section */
.data-section {
  width: 100%;
  max-width: min(90vw, 43.75rem);
  margin-top: clamp($space-md, 3vw, $space-xl);
  flex-shrink: 0;

  @media (min-width: $breakpoint-lg) {
    margin-top: 0;
    max-width: min(40vw, 25rem);
    width: auto;
    min-width: 20rem;
  }

  @media (min-width: $breakpoint-xl) {
    max-width: min(35vw, 30rem);
  }
}

.data-panel {
  background: rgba($white, 0.9);
  border-radius: clamp($space-md, 3vw, 1.25rem);
  padding: clamp($space-lg, 4vw, 1.5625rem);
  box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.05);
  width: 100%;
  animation: fadeIn 0.5s ease-in-out;
  border: 1px solid rgba($primary-color, 0.1);

  h3 {
    margin: 0 0 clamp($space-md, 3vw, 1.25rem) 0;
    color: $text-dark;
    font-size: clamp($font-lg, 3vw, $font-2xl);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: clamp($space-sm, 1.5vw, $space-md);
    border-bottom: 1px solid rgba($primary-color, 0.1);
    padding-bottom: clamp($space-sm, 2vw, $space-md);

    &::before {
      content: "📋";
      font-size: clamp($font-base, 2.5vw, $font-xl);
    }
  }
}

.data-list {
  display: flex;
  flex-direction: column;
  gap: clamp($space-sm, 2vw, 0.75rem);
  max-height: 40vh !important;
  overflow-y: auto !important;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: clamp($space-sm, 2vw, $space-md);
}

.data-item {
  padding: clamp($space-sm, 2vw, 0.75rem) clamp($space-md, 3vw, $space-md);
  background: #f8fafc;
  border-radius: clamp($space-sm, 2vw, 0.75rem);
  border-left: clamp(3px, 0.5vw, 4px) solid $success-color;

  .descInfoCategoria {
    display: block;
    font-size: clamp($font-sm, 2.5vw, $font-lg);
    color: $text-light;
    font-weight: 500;
    margin-bottom: clamp(0.2rem, 0.5vw, 0.25rem);
  }

  .descInfovalue {
    display: block;
    color: $text-dark;
    font-weight: 600;
    font-size: clamp($font-base, 3vw, $font-xl);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.progress-info {
  margin-top: clamp($space-md, 3vw, $space-lg);
  padding-top: clamp($space-sm, 2vw, $space-md);
  border-top: 1px solid rgba($primary-color, 0.1);
  
  .progress-bar {
    height: clamp(4px, 1vw, 6px);
    background: $surface-tertiary;
    border-radius: $radius-full;
    overflow: hidden;
    margin-bottom: clamp($space-xs, 1vw, $space-sm);
    
    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, $success-color, $primary-color);
      border-radius: $radius-full;
      transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      
      &::after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        animation: shimmer 2s infinite;
      }
    }
  }
  
  .progress-text {
    font-size: clamp($font-xs, 2vw, $font-sm);
    color: $text-tertiary;
    font-weight: 500;
  }
}

.history-btn {
  transition: all 0.2s ease;
  
  &:hover {
    background: rgba($primary-color, 0.1);
    color: $primary-color;
    transform: scale(1.1);
  }
}

/* Input Section */
.input-section {
  padding: clamp($space-md, 3vw, 1.25rem) clamp($space-lg, 4vw, 1.875rem) clamp($space-lg, 4vw, 1.875rem);
  background: rgba($white, 0.95);
  backdrop-filter: blur(0.625rem);
  display: flex;
  justify-content: center;
  align-items: center;
  border-top: 1px solid rgba($primary-color, 0.1);
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.05);
  
  @media (max-width: $breakpoint-xs) {
    padding: clamp($space-sm, 2vw, $space-md) clamp($space-md, 3vw, $space-lg);
  }
}

.input-container {
  max-width: min(90vw, 50rem);
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: clamp($space-sm, 2vw, $space-md);
}

.user-input {
  width: 100%;

  .mat-mdc-form-field-wrapper {
    background: $white;
    border-radius: clamp($space-lg, 4vw, 1.5625rem);
    box-shadow: $shadow;
  }

  .mat-mdc-text-field-wrapper {
    border-radius: clamp($space-lg, 4vw, 1.5625rem);
  }

  input {
    font-size: clamp($font-base, 3vw, $font-2xl);
    padding: clamp($space-sm, 2vw, $space-md) clamp($space-md, 3vw, 1.25rem);
  }
}

.voice-display {
  background: $white;
  border-radius: clamp($space-lg, 4vw, 1.5625rem);
  box-shadow: $shadow;
  padding: clamp($space-md, 3vw, 1.25rem) clamp($space-lg, 4vw, 1.875rem);
  text-align: center;
}

.voice-input-field {
  .voice-placeholder {
    color: $text-light;
    font-size: clamp($font-base, 2.5vw, $font-xl);
    font-style: italic;
  }
}

/* Audio Visualization */
.audio-visualization {
  position: fixed;
  bottom: clamp(4rem, 8vw, 6rem);
  left: 50%;
  transform: translateX(-50%);
  background: rgba($white, 0.95);
  padding: clamp($space-md, 3vw, 1.25rem) clamp($space-lg, 4vw, 1.875rem);
  border-radius: clamp($space-lg, 4vw, 1.5625rem);
  box-shadow: $shadow;
  backdrop-filter: blur(0.625rem);
  display: flex;
  align-items: center;
  gap: clamp($space-md, 3vw, 1.25rem);
  z-index: 1000;
  max-width: min(90vw, 25rem);
  border: 1px solid rgba($primary-color, 0.1);
}

.sound-wave {
  display: flex;
  align-items: center;
  gap: clamp(0.15rem, 0.4vw, 0.1875rem);
  height: clamp(2rem, 5vw, 2.5rem);
}

.wave-bar {
  width: clamp(0.2rem, 0.5vw, 0.25rem);
  background: linear-gradient(to top, $primary-color, $accent-color);
  border-radius: clamp(1px, 0.2vw, 2px);
  animation: wave-animation 1.5s infinite ease-in-out;

  &:nth-child(1) {
    animation-delay: 0s;
    height: clamp(1rem, 2.5vw, 1.25rem);
  }
  &:nth-child(2) {
    animation-delay: 0.1s;
    height: clamp(1.5rem, 3.5vw, 1.875rem);
  }
  &:nth-child(3) {
    animation-delay: 0.2s;
    height: clamp(2rem, 5vw, 2.5rem);
  }
  &:nth-child(4) {
    animation-delay: 0.3s;
    height: clamp(1.75rem, 4vw, 2.1875rem);
  }
  &:nth-child(5) {
    animation-delay: 0.4s;
    height: clamp(1.25rem, 3vw, 1.5625rem);
  }
  &:nth-child(6) {
    animation-delay: 0.5s;
    height: clamp(2rem, 5vw, 2.5rem);
  }
  &:nth-child(7) {
    animation-delay: 0.6s;
    height: clamp(1.5rem, 3.5vw, 1.875rem);
  }
  &:nth-child(8) {
    animation-delay: 0.7s;
    height: clamp(1rem, 2.5vw, 1.25rem);
  }
}

.recording-text {
  color: $text-dark;
  font-weight: 600;
  font-size: clamp($font-sm, 2.5vw, $font-lg);
  display: flex;
  align-items: center;
  gap: clamp($space-xs, 1vw, $space-sm);
  
  .recording-icon {
    color: #ff4757;
    animation: recording-pulse 1s infinite;
  }
}

/* Voice Status Indicator */
.voice-status-indicator {
  position: fixed;
  bottom: clamp($space-lg, 4vw, 1.875rem);
  right: clamp($space-lg, 4vw, 1.875rem);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: clamp($space-sm, 1.5vw, $space-md);
  z-index: 1000;
  
  @media (max-width: $breakpoint-xs) {
    bottom: clamp($space-md, 3vw, $space-lg);
    right: clamp($space-md, 3vw, $space-lg);
    scale: 0.9;
  }
}

.status-icon {
  width: clamp(3.5rem, 8vw, 4.375rem);
  height: clamp(3.5rem, 8vw, 4.375rem);
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, $text-light, #a0aec0);
  color: $white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0.5rem 1.5625rem rgba($text-light, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &.recording {
    background: linear-gradient(135deg, #ff4757, #ff3742);
    animation: recording-pulse 1s infinite;
  }

  &.processing {
    background: linear-gradient(135deg, $primary-color, $secondary-color);
    animation: processing-pulse 2s infinite;
  }
  
  &.waiting {
    background: linear-gradient(135deg, $success-color, #10b981);
    animation: waiting-pulse 2s infinite;
  }

  mat-icon {
    font-size: clamp($font-xl, 4vw, 1.75rem);
    z-index: 2;
    position: relative;
  }
}

.status-ripple {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 50%;
  background: rgba($white, 0.3);
  animation: ripple 1.5s infinite;
}

.status-text {
  background: rgba($white, 0.95);
  padding: clamp($space-xs, 1vw, 0.5rem) clamp($space-sm, 2vw, $space-md);
  border-radius: clamp($space-md, 3vw, 1.25rem);
  font-size: clamp($font-xs, 2vw, $font-sm);
  font-weight: 600;
  color: $text-dark;
  box-shadow: 0 0.25rem 0.9375rem rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(0.625rem);
  white-space: nowrap;
}
/* #endregion */

/* #region Animações */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes blink {
  0%,
  90%,
  100% {
    transform: scaleY(1);
  }
  95% {
    transform: scaleY(0.1);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes ai-blink {
  0%,
  90%,
  100% {
    transform: scaleY(1);
  }
  95% {
    transform: scaleY(0.1);
  }
}

@keyframes mouth-talk {
  0% {
    transform: scaleY(1);
  }
  100% {
    transform: scaleY(1.5);
  }
}

@keyframes processing-pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes listening-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes waiting-pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}

@keyframes typing {
  0%,
  60%,
  100% {
    transform: translateY(0);
  }
  30% {
    transform: translateY(-10px);
  }
}

@keyframes wave-animation {
  0%,
  100% {
    transform: scaleY(0.5);
  }
  50% {
    transform: scaleY(1);
  }
}

@keyframes recording-pulse {
  0%,
  100% {
    box-shadow: 0 8px 25px rgba(255, 71, 87, 0.4);
  }
  50% {
    box-shadow: 0 8px 35px rgba(255, 71, 87, 0.8);
  }
}

@keyframes ripple {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

// Melhorias nas animações existentes
@keyframes waiting-pulse {
  0%, 100% {
    box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
  }
  50% {
    box-shadow: 0 8px 35px rgba(16, 185, 129, 0.8);
  }
}
/* #endregion */

/* #region Enhanced Responsividade */

// Extra small devices (phones, 480px and down)
@media (max-width: $breakpoint-xs) {
  .ai-questionnaire-container {
    font-size: clamp(0.9rem, 2.2vw, 1rem);
  }

  .idle-content {
    padding: clamp($space-lg, 6vw, $space-2xl) clamp($space-md, 4vw, $space-lg);
    margin: $space-md;

    h1 {
      font-size: clamp($font-xl, 4.5vw, $font-2xl);
      margin: clamp($space-md, 3vw, $space-lg) 0 clamp($space-sm, 1.5vw, $space-md) 0;
    }

    p {
      font-size: clamp($font-sm, 2.8vw, $font-base);
      margin-bottom: clamp($space-md, 4vw, $space-lg);
    }
  }

  .chat-interface {
    min-height: 100vh;
    min-height: 100dvh; // Dynamic viewport height for mobile
  }

  .controls-header {
    position: static;
    margin: $space-md auto $space-lg;
    padding: clamp($space-xs, 1.5vw, $space-sm) clamp($space-sm, 2.5vw, $space-md);
    gap: clamp($space-xs, 1.5vw, $space-sm);
    justify-content: center;
    max-width: 90vw;
    border-radius: clamp($space-md, 3vw, $space-lg);
  }

  .main-chat-area {
    padding: clamp($space-lg, 4vw, $space-xl) clamp($space-sm, 3vw, $space-md) clamp($space-md, 3vw, $space-lg);
    gap: clamp($space-md, 3vw, $space-lg);
    max-width: 100vw;
  }

  .response-data-section {
    flex-direction: column;
    align-items: center;
    gap: clamp($space-md, 3vw, $space-lg);
  }

  .ai-avatar {
    width: clamp(3.5rem, 10vw, 4.5rem);
    height: clamp(3.5rem, 10vw, 4.5rem);
  }

  .message-bubble {
    padding: clamp($space-sm, 2.5vw, $space-md) clamp($space-md, 3.5vw, $space-lg);
    border-radius: clamp($space-md, 3vw, $space-lg);

    p {
      font-size: clamp($font-sm, 2.8vw, $font-base);
    }
  }

  .data-section {
    max-width: 100%;
    margin-top: clamp($space-sm, 2vw, $space-md);
  }

  .data-panel {
    padding: clamp($space-md, 3vw, $space-lg);
    border-radius: clamp($space-md, 3vw, $space-lg);
  }

  .input-section {
    padding: clamp($space-sm, 2vw, $space-md) clamp($space-md, 3vw, $space-lg) clamp($space-md, 3vw, $space-lg);
  }

  .user-input {
    .mat-mdc-form-field-wrapper {
      border-radius: clamp($space-md, 3vw, $space-lg);
    }
  }

  .audio-visualization {
    bottom: clamp(5rem, 10vw, 6rem);
    padding: clamp($space-sm, 2.5vw, $space-md) clamp($space-md, 3.5vw, $space-lg);
    border-radius: clamp($space-md, 3vw, $space-lg);
    left: 50%;
    transform: translateX(-50%);
    max-width: 90vw;
  }

  .voice-status-indicator {
    bottom: clamp($space-md, 3vw, $space-lg);
    right: clamp($space-md, 3vw, $space-lg);
  }

  .status-icon {
    width: clamp(3rem, 7vw, 3.5rem);
    height: clamp(3rem, 7vw, 3.5rem);

    mat-icon {
      font-size: clamp($font-lg, 3.5vw, $font-xl);
    }
  }

  .status-text {
    font-size: clamp($font-xs, 1.8vw, $font-sm);
    padding: clamp($space-xs, 0.8vw, 0.4rem) clamp($space-sm, 1.5vw, $space-sm);
  }
}

// Small devices (landscape phones, 481px to 640px)
@media (min-width: #{$breakpoint-xs + 1px}) and (max-width: $breakpoint-sm) {
  .main-chat-area {
    padding: clamp(5rem, 10vw, 6rem) clamp($space-md, 3vw, $space-lg) clamp($space-lg, 3vw, $space-xl);
  }

  .controls-header {
    position: absolute;
    top: clamp($space-md, 2.5vw, $space-lg);
    left: clamp($space-md, 2.5vw, $space-lg);
  }

  .response-data-section {
    grid-template-columns: 1fr;
    gap: clamp($space-lg, 4vw, $space-xl);
  }

  .data-section {
    grid-column: 1;
    margin-top: clamp($space-md, 3vw, $space-xl);
  }
}

// Medium devices (tablets, 641px to 768px)
@media (min-width: #{$breakpoint-sm + 1px}) and (max-width: $breakpoint-md) {
  .main-chat-area {
    padding: clamp(6rem, 10vw, 7rem) clamp($space-lg, 4vw, $space-xl) clamp($space-xl, 4vw, $space-2xl);
  }

  .response-data-section {
    flex-direction: column;
    gap: clamp($space-xl, 5vw, $space-2xl);
    align-items: center;
  }

  .data-section {
    max-width: 100%;
    margin-top: clamp($space-lg, 4vw, $space-xl);
    width: 100%;
  }

  .response-section {
    align-items: center;
    width: 100%;
  }

  .controls-header {
    top: clamp($space-md, 2.5vw, $space-lg);
    left: clamp($space-md, 2.5vw, $space-lg);
    right: clamp($space-md, 2.5vw, $space-lg);
    width: auto;
    justify-content: center;
  }
}

// Large devices (small laptops, 769px to 1024px)
@media (min-width: #{$breakpoint-md + 1px}) and (max-width: $breakpoint-lg) {
  .main-chat-area {
    max-width: min(90vw, 75rem);
  }

  .response-data-section {
    flex-direction: column;
    gap: clamp($space-lg, 3vw, $space-2xl);
    align-items: center;
  }

  .data-section {
    margin-top: clamp($space-lg, 3vw, $space-xl);
    max-width: 100%;
    width: 100%;
  }

  .response-section {
    width: 100%;
  }
}

// Extra large devices (large laptops and desktops, 1025px and up)
@media (min-width: #{$breakpoint-lg + 1px}) {
  .main-chat-area {
    max-width: min(85vw, 85rem);
  }

  .response-data-section {
    flex-direction: row;
    align-items: flex-start;
    gap: clamp($space-xl, 2vw, $space-3xl);
  }

  .response-section {
    flex: 1;
    max-width: none;
  }

  .data-section {
    flex-shrink: 0;
    width: auto;
    min-width: 20rem;
    margin-top: 0;
  }
}

// Landscape orientation adjustments
@media (orientation: landscape) and (max-height: 600px) {
  .idle-screen {
    padding: clamp($space-sm, 2vh, $space-md);
  }

  .idle-content {
    padding: clamp($space-lg, 4vh, $space-xl) clamp($space-lg, 4vw, $space-2xl);

    h1 {
      font-size: clamp($font-lg, 4vw, $font-xl);
      margin: clamp($space-sm, 2vh, $space-md) 0 clamp($space-xs, 1vh, $space-sm) 0;
    }

    p {
      font-size: clamp($font-sm, 2.5vw, $font-base);
      margin-bottom: clamp($space-md, 3vh, $space-lg);
    }
  }

  .main-chat-area {
    padding: clamp(4rem, 8vh, 5rem) clamp($space-lg, 4vw, $space-xl) clamp($space-md, 2vh, $space-lg);
  }

  .ai-avatar {
    width: clamp(4rem, 8vh, 5rem);
    height: clamp(4rem, 8vh, 5rem);
  }
}

// High DPI displays
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .ai-avatar,
  .robot-head,
  .status-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

// Container queries for modern browsers
@container (max-width: 480px) {
  .message-bubble {
    padding: clamp($space-sm, 2vw, $space-md);

    p {
      font-size: clamp($font-sm, 2.5vw, $font-base);
    }
  }
}

// Reduced motion preferences
@media (prefers-reduced-motion: reduce) {
  .ai-robot,
  .ai-avatar,
  .start-btn,
  .status-icon {
    animation: none;
  }

  .btn-glow {
    transition: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .message-bubble,
  .data-panel,
  .controls-header {
    border: 2px solid $text-dark;
  }

  .start-btn {
    border: 2px solid $white;
  }
}

/* #endregion */

/* #region Modern History Modal */

// Spacing system (8px base)
$space-xs: 0.25rem; // 4px
$space-sm: 0.5rem; // 8px
$space-md: 1rem; // 16px
$space-lg: 1.5rem; // 24px
$space-xl: 2rem; // 32px
$space-2xl: 3rem; // 48px
$space-3xl: 4rem; // 64px

// Border radius scale
$radius-sm: 0.375rem; // 6px
$radius-md: 0.5rem; // 8px
$radius-lg: 0.75rem; // 12px
$radius-xl: 1rem; // 16px
$radius-2xl: 1.5rem; // 24px
$radius-full: 9999px;

// Shadow system
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md:
  0 4px 6px -1px rgba(0, 0, 0, 0.1),
  0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg:
  0 10px 15px -3px rgba(0, 0, 0, 0.1),
  0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl:
  0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04);
$shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

// Typography scale
$text-xs: 0.75rem; // 12px
$text-sm: 0.875rem; // 14px
$text-base: 1rem; // 16px
$text-lg: 1.125rem; // 18px
$text-xl: 1.25rem; // 20px
$text-2xl: 1.5rem; // 24px
$text-3xl: 1.875rem; // 30px

// Breakpoints
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;

// Z-index scale
$z-modal: 1000;
$z-overlay: 999;
$z-dropdown: 50;

.modern-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: $surface-overlay;
  backdrop-filter: blur(8px) saturate(180%);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: $z-modal;
  padding: $space-md;

  // Enhanced accessibility
  &:focus-within {
    outline: 2px solid $modal-accent;
    outline-offset: -2px;
  }
}

.modern-modal-container {
  background: $surface-elevated;
  border-radius: $radius-2xl;
  box-shadow: $shadow-2xl;
  width: 100%;
  max-width: 56rem; // 896px
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);

  // Glass morphism effect
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);

  @media (max-width: $breakpoint-md) {
    max-width: 95vw;
    max-height: 95vh;
    margin: $space-sm;
  }
}

// Modal Header
.modal-header-modern {
  padding: $space-xl $space-xl $space-lg;
  border-bottom: 1px solid $surface-tertiary;
  background: linear-gradient(135deg, rgba($modal-primary, 0.05) 0%, rgba($modal-secondary, 0.05) 100%);

  .header-content {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    gap: $space-lg;
    margin-bottom: $space-lg;
  }

  .title-section {
    display: flex;
    align-items: flex-start;
    gap: $space-md;
    flex: 1;
  }

  .icon-wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    background: linear-gradient(135deg, $modal-primary, $modal-secondary);
    border-radius: $radius-xl;
    box-shadow: $shadow-md;

    .header-icon {
      color: $text-inverse;
      font-size: 1.5rem;
    }
  }

  .title-text {
    flex: 1;

    .modal-title {
      margin: 0 0 $space-xs 0;
      font-size: $text-2xl;
      font-weight: 700;
      color: $text-primary;
      line-height: 1.2;
    }

    .modal-subtitle {
      margin: 0;
      font-size: $text-sm;
      color: $text-secondary;
      font-weight: 500;
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: $space-sm;

    .action-btn {
      width: 2.5rem;
      height: 2.5rem;
      border-radius: $radius-lg;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      &.secondary {
        background: $surface-secondary;
        color: $text-secondary;

        &:hover {
          background: $surface-tertiary;
          color: $text-primary;
          transform: translateY(-1px);
          box-shadow: $shadow-md;
        }
      }

      &.close-btn {
        background: rgba($modal-error, 0.1);
        color: $modal-error;

        &:hover {
          background: rgba($modal-error, 0.15);
          transform: translateY(-1px);
          box-shadow: $shadow-md;
        }
      }
    }
  }

  // Progress Section
  .progress-section {
    .progress-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: $space-sm;

      .progress-text {
        font-size: $text-sm;
        color: $text-secondary;
        font-weight: 500;
      }

      .progress-percentage {
        font-size: $text-sm;
        color: $modal-accent;
        font-weight: 700;
      }
    }

    .progress-bar {
      height: 0.5rem;
      background: $surface-tertiary;
      border-radius: $radius-full;
      overflow: hidden;

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, $modal-accent, $modal-primary);
        border-radius: $radius-full;
        transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;

        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
          animation: shimmer 2s infinite;
        }
      }
    }
  }
}

// Search and Filter Section
.search-filter-section {
  padding: $space-lg $space-xl;
  border-bottom: 1px solid $surface-tertiary;
  background: $surface-secondary;

  .search-container {
    margin-bottom: $space-md;

    .search-field {
      width: 100%;

      .mat-mdc-form-field-wrapper {
        background: $surface-primary;
        border-radius: $radius-xl;
        box-shadow: $shadow-sm;
        border: 1px solid $surface-tertiary;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

        &:hover {
          border-color: rgba($modal-primary, 0.3);
          box-shadow: $shadow-md;
        }

        &:focus-within {
          border-color: $modal-primary;
          box-shadow: 0 0 0 3px rgba($modal-primary, 0.1);
        }
      }
    }
  }

  .filter-chips {
    .filter-chip-list {
      display: flex;
      flex-wrap: wrap;
      gap: $space-sm;

      .mat-mdc-chip-option {
        border-radius: $radius-full;
        font-weight: 500;
        font-size: $text-sm;
        transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

        &.filter-chip-personal {
          --mdc-chip-selected-container-color: #{rgba($modal-primary, 0.1)};
          --mdc-chip-selected-label-text-color: #{$modal-primary};
        }

        &.filter-chip-medical {
          --mdc-chip-selected-container-color: #{rgba($modal-success, 0.1)};
          --mdc-chip-selected-label-text-color: #{$modal-success};
        }

        &.filter-chip-contact {
          --mdc-chip-selected-container-color: #{rgba($modal-info, 0.1)};
          --mdc-chip-selected-label-text-color: #{$modal-info};
        }

        &.filter-chip-optional {
          --mdc-chip-selected-container-color: #{rgba($modal-warning, 0.1)};
          --mdc-chip-selected-label-text-color: #{$modal-warning};
        }
      }
    }
  }
}

// Modal Body
.modal-body-modern {
  flex: 1;
  overflow-y: auto;
  padding: $space-lg $space-xl;

  .content-wrapper {
    max-height: 100%;
  }

  // Empty State
  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: $space-3xl $space-lg;
    text-align: center;

    .empty-icon {
      width: 4rem;
      height: 4rem;
      background: $surface-tertiary;
      border-radius: $radius-full;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: $space-lg;

      mat-icon {
        font-size: 2rem;
        color: $text-tertiary;
      }
    }

    .empty-title {
      margin: 0 0 $space-sm 0;
      font-size: $text-xl;
      font-weight: 600;
      color: $text-primary;
    }

    .empty-description {
      margin: 0;
      font-size: $text-sm;
      color: $text-secondary;
      max-width: 24rem;
    }
  }

  // Data Grid
  .data-grid {
    display: grid;
    gap: $space-md;
    grid-template-columns: 1fr;

    @media (min-width: $breakpoint-lg) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  // Data Cards
  .data-card {
    background: $surface-primary;
    border: 1px solid $surface-tertiary;
    border-radius: $radius-xl;
    padding: $space-lg;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, $modal-primary, $modal-secondary);
      opacity: 0;
      transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    &:hover {
      border-color: rgba($modal-primary, 0.2);
      box-shadow: $shadow-lg;
      transform: translateY(-2px);

      &::before {
        opacity: 1;
      }
    }

    // Category-specific styling
    &.card-personal {
      border-left: 4px solid $modal-primary;
    }

    &.card-medical {
      border-left: 4px solid $modal-success;
    }

    &.card-contact {
      border-left: 4px solid $modal-info;
    }

    &.card-optional {
      border-left: 4px solid $modal-warning;
    }

    .card-header {
      display: flex;
      align-items: flex-start;
      gap: $space-md;
      margin-bottom: $space-md;

      .card-icon {
        width: 2.5rem;
        height: 2.5rem;
        background: $surface-secondary;
        border-radius: $radius-lg;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;

        mat-icon {
          font-size: 1.25rem;
          color: $text-secondary;
        }
      }

      .card-title-section {
        flex: 1;
        min-width: 0;

        .card-title {
          margin: 0 0 $space-xs 0;
          font-size: $text-base;
          font-weight: 600;
          color: $text-primary;
          line-height: 1.3;
        }

        .card-category {
          font-size: $text-xs;
          color: $text-tertiary;
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.05em;
        }
      }

      .card-actions {
        display: flex;
        gap: $space-xs;
        opacity: 0;
        transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);

        .card-action-btn {
          width: 2rem;
          height: 2rem;
          border-radius: $radius-md;
          background: $surface-secondary;
          color: $text-secondary;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

          mat-icon {
            font-size: 1rem;
          }

          &:hover {
            background: $modal-primary;
            color: $text-inverse;
            transform: scale(1.1);
          }
        }
      }
    }

    &:hover .card-actions {
      opacity: 1;
    }

    .card-content {
      .value-container {
        margin-bottom: $space-md;

        .card-value {
          font-size: $text-base;
          color: $text-primary;
          font-weight: 500;
          line-height: 1.5;
          word-break: break-word;

          ::ng-deep mark {
            background: rgba($modal-accent, 0.2);
            color: $modal-accent;
            padding: 0.125rem 0.25rem;
            border-radius: $radius-sm;
            font-weight: 600;
          }
        }
      }

      .card-metadata {
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: $space-sm;
        padding-top: $space-sm;
        border-top: 1px solid $surface-tertiary;

        .timestamp {
          display: flex;
          align-items: center;
          gap: $space-xs;
          font-size: $text-xs;
          color: $text-tertiary;

          mat-icon {
            font-size: 0.875rem;
          }
        }

        .validation-status {
          display: flex;
          align-items: center;
          gap: $space-xs;
          font-size: $text-xs;
          font-weight: 500;
          padding: $space-xs $space-sm;
          border-radius: $radius-full;

          mat-icon {
            font-size: 0.875rem;
          }

          &.status-valid {
            background: rgba($modal-success, 0.1);
            color: $modal-success;
          }

          &.status-warning {
            background: rgba($modal-warning, 0.1);
            color: $modal-warning;
          }

          &.status-error {
            background: rgba($modal-error, 0.1);
            color: $modal-error;
          }
        }
      }
    }
  }
}

// Modal Footer
.modal-footer-modern {
  padding: $space-lg $space-xl;
  border-top: 1px solid $surface-tertiary;
  background: $surface-secondary;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: $space-lg;

  .footer-info {
    .info-text {
      display: flex;
      align-items: center;
      gap: $space-sm;
      font-size: $text-sm;
      color: $text-secondary;

      mat-icon {
        font-size: 1rem;
        color: $modal-info;
      }
    }
  }

  .footer-actions {
    display: flex;
    gap: $space-md;

    .secondary-btn {
      border-radius: $radius-lg;
      font-weight: 500;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: $shadow-md;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .primary-btn {
      border-radius: $radius-lg;
      font-weight: 600;
      background: linear-gradient(135deg, $modal-primary, $modal-secondary);
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-1px);
        box-shadow: $shadow-lg;
        background: linear-gradient(135deg, darken($modal-primary, 5%), darken($modal-secondary, 5%));
      }
    }
  }

  @media (max-width: $breakpoint-sm) {
    flex-direction: column;
    align-items: stretch;
    gap: $space-md;

    .footer-actions {
      justify-content: stretch;

      .secondary-btn,
      .primary-btn {
        flex: 1;
      }
    }
  }
}

// Animations
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// Accessibility enhancements
@media (prefers-reduced-motion: reduce) {
  .modern-modal-container,
  .data-card,
  .action-btn,
  .card-action-btn,
  .secondary-btn,
  .primary-btn {
    transition: none;
  }

  .progress-fill::after {
    animation: none;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .modern-modal-container {
    border: 2px solid $text-primary;
  }

  .data-card {
    border: 2px solid $text-secondary;
  }

  .search-field .mat-mdc-form-field-wrapper {
    border: 2px solid $text-secondary;
  }
}

// Dark mode support (if needed in the future)
@media (prefers-color-scheme: dark) {
  // Dark mode variables would go here
  // This is prepared for future dark mode implementation
}
/* #endregion */

/* #region Botão de Teste */
.test-button {
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 25px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  font-size: 0.9em !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3) !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  overflow: hidden !important;

  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4) !important;
    background: linear-gradient(135deg, #f57c00 0%, #e65100 100%) !important;
  }

  &:active {
    transform: translateY(0) !important;
  }

  mat-icon {
    margin-right: 8px !important;
    font-size: 1.1em !important;
  }

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  &:hover::before {
    left: 100%;
  }
}

// Estilo específico para o botão na tela inicial
.idle-content .test-button {
  margin-top: 10px;
  font-size: 0.85em !important;
  padding: 10px 20px !important;
}

// Estilo específico para o botão no header de controles
.controls-header .test-button {
  margin-left: 15px;
  font-size: 0.8em !important;
  padding: 8px 16px !important;
}
/* #endregion */
