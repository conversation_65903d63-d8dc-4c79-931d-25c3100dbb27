<div class="ai-questionnaire-container">
  <!-- <PERSON><PERSON> -->
  <div class="idle-screen" *ngIf="isIdle">
    <div class="idle-content">
      <!-- IA Robot Custom CSS -->
      <div class="ai-robot">
        <div class="robot-head">
          <div class="robot-eyes">
            <div class="eye left-eye"></div>
            <div class="eye right-eye"></div>
          </div>
          <div class="robot-mouth"></div>
        </div>
        <div class="robot-body">
          <div class="robot-chest"></div>
        </div>
      </div>

      <p>Vou coletar suas informações através de uma conversa natural</p>

      <div style="display: flex; flex-direction: column; gap: 15px; align-items: center;">
        <button class="start-btn" (click)="iniciarAtendimento()">
          <span>Iniciar Atendimento</span>
          <div class="btn-glow"></div>
        </button>

        <!-- Botão de teste na tela inicial -->
      </div>
    </div>
  </div>

  <!-- Tela de Atendimento -->
  <div class="chat-interface" *ngIf="!isIdle">
    <!-- Controls Header - Position Absolute Top Left -->
    <div class="controls-header">
      <div class="mode-indicator">
        <mat-icon class="mode-icon">{{ isTextMode ? 'keyboard' : 'mic' }}</mat-icon>
      </div>
      <mat-slide-toggle [(ngModel)]="isTextMode" (change)="toggleTextMode()" class="mode-toggle">
        Modo Texto
      </mat-slide-toggle>
    </div>

    <!-- Main Chat Area -->
    <div class="main-chat-area">
      <!-- IA Section -->
      <div class="ai-section">
        <div class="ai-avatar" [class.processing]="isProcessing" [class.listening]="isRecording"
          [class.waiting]="isAguardandoResposta && !isRecording">
          <div class="ai-face">
            <div class="ai-eyes">
              <div class="eye"></div>
              <div class="eye"></div>
            </div>
            <div class="ai-mouth" [class.talking]="isProcessing"></div>
          </div>
          <div class="ai-pulse" *ngIf="isProcessing || isRecording || isAguardandoResposta"></div>
        </div>
      </div>

      <!-- Response and Data Section -->
      <div class="response-data-section"
        style="width: 100%; display: flex; justify-content: space-evenly; align-items: center; gap: 20px;">
        <div class="response-section">
          <!-- Exibição sincronizada do texto -->
          <div class="ai-message" *ngIf="aiResponse || displayedText || fullResponseText">
            <div class="message-bubble" [class.speaking]="isSpeaking">
              <p>{{ displayedText || fullResponseText || aiResponse }}</p>
              <div class="speaking-indicator" *ngIf="isSpeaking">
                <div class="sound-wave-mini">
                  <div class="wave-bar-mini" *ngFor="let bar of [1,2,3,4,5]"></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Processing Indicator Aprimorado -->
          <div class="enhanced-processing-indicator" *ngIf="isProcessing">
            <div class="ai-thinking-animation">
              <div class="brain-icon">
                <mat-icon>psychology</mat-icon>
              </div>
              <div class="thinking-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
            <p class="processing-text">Processando sua resposta...</p>
          </div>
        </div>

        <!-- Dados Preenchidos -->
        <div class="data-section" *ngIf="getDadosPreenchidos().length > 0">
          <div class="data-panel">
            <div class="data-header">
              <h3>Última Informação Coletada</h3>
              <button mat-icon-button (click)="openHistoryModal()" matTooltip="Ver todas as variáveis">
                <mat-icon>history</mat-icon>
              </button>
            </div>
            <div class="data-item" *ngIf="getUltimaVariavelPreenchida() as lastItem" [matTooltip]="lastItem.value">
              <span class="descInfoCategoria">{{ lastItem.label }}</span>
              <span class="descInfovalue">{{ lastItem.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Input Area -->
    <div class="input-section">
      <div class="input-container">
        <!-- Text Input -->
        <mat-form-field appearance="outline" class="user-input" *ngIf="isTextMode">
          <mat-label>Sua resposta</mat-label>
          <input matInput [(ngModel)]="userInput" placeholder="Digite aqui..." (keyup.enter)="enviarTexto()"
            [disabled]="isInputDisabled()">
          <button mat-icon-button matSuffix *ngIf="canSendText()" (click)="enviarTexto()" [disabled]="!canSendText()">
            <mat-icon>send</mat-icon>
          </button>
          <!-- Indicador de que está aguardando fala terminar -->
          <mat-hint *ngIf="isSpeaking" class="speaking-hint">
            <mat-icon class="hint-icon">volume_up</mat-icon>
            Aguarde o término da fala...
          </mat-hint>
        </mat-form-field>

      </div>
    </div>

    <!-- Audio Visualization -->
    <div class="audio-visualization" *ngIf="isRecording && !isTextMode">
      <div class="sound-wave">
        <div class="wave-bar" *ngFor="let bar of [1,2,3,4,5,6,7,8]"></div>
      </div>
      <span class="recording-text">Gravando...</span>
    </div>

    <!-- Voice Status Indicator (Floating) -->
    <div class="voice-status-indicator" *ngIf="!isTextMode">
      <div class="status-icon" [class.recording]="isRecording" [class.processing]="isProcessing">
        <mat-icon>{{ isRecording ? 'mic' : isProcessing ? 'hourglass_empty' : 'mic_off' }}</mat-icon>
        <div class="status-ripple" *ngIf="isRecording"></div>
      </div>
      <span class="status-text">
        {{ isRecording ? 'Ouvindo...' :
        isProcessing ? 'Processando...' :
        isAguardandoResposta ? 'Aguardando sua resposta...' :
        'Aguardando...' }}
      </span>
    </div>
  </div>

</div>