import { CommonModule } from '@angular/common';
import { Component, Inject, OnInit, ChangeDetectorRef } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatDividerModule } from '@angular/material/divider';
import { finalize, catchError } from 'rxjs/operators';
import { of } from 'rxjs';
import { LogRequisicaoExameModelView } from 'src/app/model/IntegrationApiExame';
import { IntegrationService } from '../../pre-consulta-questionario/Service/integracao-vittal-tec.service';
import { CriptografarUtil } from 'src/app/Util/Criptografar.util';

@Component({
  selector: 'app-modal-coleta-dados-vittaltec',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatSelectModule,
    MatCheckboxModule,
    MatIconModule,
    MatDialogModule,
    MatProgressBarModule,
    MatDividerModule
  ],
  templateUrl: './modal-coleta-dados-vittaltec.component.html',
  styleUrl: './modal-coleta-dados-vittaltec.component.scss'
})
export class ModalColetaDadosVittaltecComponent implements OnInit {
  isLoading = false;
  currentStep = 0;
  errorMessage = '';
  capturedData: any = null;
  showDataPreview = false;
  steps = [
    { label: 'Verificando conectividade...', completed: false, error: false },
    { label: 'Capturando dados...', completed: false, error: false },
    { label: 'Lendo informações...', completed: false, error: false },
    { label: 'Alternando janela do navegador...', completed: false, error: false }
  ];

  constructor(
    public dialogRef: MatDialogRef<ModalColetaDadosVittaltecComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private integrationService: IntegrationService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    console.clear();
    this.startIntegrationProcess();
  }

  startIntegrationProcess(): void {
    this.isLoading = true;
    this.currentStep = 0;
    this.errorMessage = '';
    this.capturedData = null;
    this.showDataPreview = false;
    this.steps.forEach(step => {
      step.completed = false;
      step.error = false;
    });
    this.executeHealthCheck();
  }

  private executeHealthCheck(): void {
    this.currentStep = 0;
    this.cdr.detectChanges();

    this.integrationService.healthCheck()
      .pipe(
        catchError((error) => {
          console.error('Health check error caught:', error);
          this.steps[0].error = true;
          this.errorMessage = 'Falha na verificação de conectividade. Verifique se o serviço VittalTec está rodando na porta 8080.';
          this.isLoading = false;
          this.registrarLog('healthCheck', 'ERRO', JSON.stringify(error));
          this.cdr.detectChanges();
          return of(null);
        }),
        finalize(() => {
          setTimeout(() => {
            if (!this.steps[0].error) {
              this.steps[0].completed = true;
              this.cdr.detectChanges();
              this.executeCapture();
            }
          }, 500);
        })
      )
      .subscribe({
        next: (response) => {
          if (response) {
            this.registrarLog('healthCheck', 'SUCESSO', JSON.stringify(response));
          }
        },
        error: (error) => {
          console.error('Health check subscription error:', error);
          this.steps[0].error = true;
          this.errorMessage = 'Erro inesperado na verificação de conectividade.';
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  private executeCapture(): void {
    this.currentStep = 1;
    this.cdr.detectChanges();

    this.integrationService.capture()
      .pipe(
        catchError((error) => {
          console.error('Capture error caught:', error);
          this.steps[1].error = true;
          this.errorMessage = 'Falha na captura dos dados. Verifique se o dispositivo VittalTec está conectado e funcionando.';
          this.isLoading = false;
          this.registrarLog('capture', 'ERRO', JSON.stringify(error));
          this.cdr.detectChanges();
          return of(null);
        }),
        finalize(() => {
          setTimeout(() => {
            if (!this.steps[1].error) {
              this.steps[1].completed = true;
              this.cdr.detectChanges();
              this.executeRead();
            }
          }, 500);
        })
      )
      .subscribe({
        next: (response) => {
          if (response) {
            this.registrarLog('capture', 'SUCESSO', JSON.stringify(response));
          }
        },
        error: (error) => {
          console.error('Capture subscription error:', error);
          this.steps[1].error = true;
          this.errorMessage = 'Erro inesperado na captura de dados.';
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  private executeRead(): void {
    this.currentStep = 2;
    this.cdr.detectChanges();

    this.integrationService.read()
      .pipe(
        catchError((error) => {
          console.error('Read error caught:', error);
          this.steps[2].error = true;
          this.errorMessage = 'Falha na leitura dos dados. Verifique se os dados foram capturados corretamente.';
          this.isLoading = false;
          this.registrarLog('read', 'ERRO', JSON.stringify(error));
          this.cdr.detectChanges();
          return of(null);
        }),
        finalize(() => {
          setTimeout(() => {
            if (!this.steps[2].error) {
              this.steps[2].completed = true;
              this.cdr.detectChanges();
              if (this.capturedData) {
                this.showDataPreview = true;
                this.isLoading = false;
              } else {
                this.executeSwitchWindow();
              }
            }
          }, 500);
        })
      )
      .subscribe({
        next: (response) => {
          if (response) {
            this.registrarLog('read', 'SUCESSO', JSON.stringify(response));
            console.log("response", response);
            if (response && (Array.isArray(response) ? response.length > 0 : Object.keys(response).length > 0)) {
              this.capturedData = response;
              CriptografarUtil.localStorageCriptografado("VittalTecDados", response);
            } else {
              this.steps[2].error = true;
              this.errorMessage = 'Nenhum dado foi encontrado para leitura.';
              this.isLoading = false;
            }
          }
        },
        error: (error: any) => {
          console.error('Read subscription error:', error);
          this.steps[2].error = true;
          this.errorMessage = 'Erro inesperado na leitura de dados.';
          this.isLoading = false;
          this.cdr.detectChanges();
        }
      });
  }

  private executeSwitchWindow(): void {
    this.currentStep = 3;

    this.integrationService.switchWindowBrowser()
      .pipe(
        finalize(() => {
          setTimeout(() => {
            if (!this.steps[3].error) {
              this.steps[3].completed = true;
              this.isLoading = false;
              setTimeout(() => {
                this.onContinue();
              }, 1000);
            }
          }, 500);
        })
      )
      .subscribe({
        next: (response) => {
          this.registrarLog('switchWindowBrowser', '', JSON.stringify(response));
        },
        error: (error) => {
          console.error('Switch window failed:', error);
          this.steps[3].error = true;
          this.errorMessage = 'Falha ao alternar a janela do navegador.';
          this.isLoading = false;
          this.registrarLog('switchWindowBrowser', '', JSON.stringify(error));
        }
      });
  }

  private registrarLog(endpoint: string, requisicao: string, resposta: string): void {
    const logData: LogRequisicaoExameModelView = {
      Id: null,
      Requisicao: `${endpoint}: ${requisicao}`,
      Resposta: resposta,
      DtCadastro: new Date()
    };

    this.integrationService.RegistraLogRequisicao(logData).subscribe({
      next: (response) => {
        response;
      },
      error: (error) => {
        console.error('Erro ao registrar log:', error);
      }
    });
  }

  continueProcess(): void {
    this.showDataPreview = false;
    this.isLoading = true;
    this.executeSwitchWindow();
  }

  areAllStepsCompleted(): boolean {
    return this.steps.every(step => step.completed);
  }

  retry(): void {
    this.startIntegrationProcess();
  }

  startProcess(): void {
    this.startIntegrationProcess();
  }

  onContinue(): void {
    this.dialogRef.close({ action: 'continuar', data: this.capturedData });
  }

  onCancel(): void {
    this.dialogRef.close({ action: 'cancelar' });
  }

  onClose(): void {
    this.dialogRef.close();
  }
}