import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, timer } from 'rxjs';
import { retry, catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { IntegrationApiExameModelView, LogRequisicaoExameModelView } from '../../../model/IntegrationApiExame';

@Injectable({
  providedIn: 'root',
})
export class IntegrationService {
  // private readonly baseUrl = 'http://localhost:5117/Integration';
  private readonly baseUrl = environment.apiEndpoint + '/Integration';
  private readonly endpointApi = environment.apiEndpoint + '/Integration';

  // Retry configuration
  private readonly retryConfig = {
    maxRetries: 3,
    baseDelay: 1000, // 1 second
    maxDelay: 10000, // 10 seconds
    backoffMultiplier: 2
  };

  constructor(
    private readonly http: HttpClient
  ) { }

  /**
   * Creates a retry strategy with exponential backoff
   * @param maxRetries Maximum number of retry attempts
   * @param baseDelay Base delay in milliseconds
   * @param isIdempotent Whether the operation is idempotent (safe to retry)
   */
  private createRetryStrategy(maxRetries: number = this.retryConfig.maxRetries, baseDelay: number = this.retryConfig.baseDelay, isIdempotent: boolean = true) {
    return retry({
      count: maxRetries,
      delay: (error: HttpErrorResponse, retryCount: number) => {
        // Don't retry on client errors (4xx) for non-idempotent operations
        if (!isIdempotent && error.status >= 400 && error.status < 500) {
          throw error;
        }

        // Don't retry on authentication/authorization errors
        if (error.status === 401 || error.status === 403) {
          throw error;
        }

        // Calculate exponential backoff delay
        const delay = Math.min(
          baseDelay * Math.pow(this.retryConfig.backoffMultiplier, retryCount - 1),
          this.retryConfig.maxDelay
        );

        console.warn(`HTTP request failed (attempt ${retryCount}/${maxRetries}). Retrying in ${delay}ms...`, {
          status: error.status,
          message: error.message,
          url: error.url
        });

        return timer(delay);
      }
    });
  }

  /**
   * Enhanced error handling with logging
   */
  private handleError(operation: string) {
    return catchError((error: HttpErrorResponse) => {
      console.error(`${operation} failed after all retry attempts:`, {
        status: error.status,
        message: error.message,
        url: error.url,
        error: error.error
      });

      // Re-throw the error to be handled by the calling component
      throw error;
    });
  }

  //#region Metodos aparelho vittal-tec
  /**
   * Health check for VittalTec device
   * Uses retry logic with exponential backoff for network resilience
   */
  healthCheck(): Observable<any> {
    const url = `${this.baseUrl}/healthcheck`;
    return this.http.get(url, { headers: new HttpHeaders({ accept: '*/*' }) })
      .pipe(
        this.createRetryStrategy(3, 1000, true), // GET is idempotent, safe to retry
        this.handleError('healthCheck')
      );
  }

  /**
   * Capture data from VittalTec device
   * Uses limited retry for device operations to avoid overwhelming the hardware
   */
  capture(): Observable<any> {
    const url = `${this.baseUrl}/capture`;
    return this.http.post(url, '', { headers: new HttpHeaders({ accept: '*/*' }) })
      .pipe(
        this.createRetryStrategy(2, 2000, false), // POST to device, limited retries
        this.handleError('capture')
      );
  }

  /**
   * Read data from VittalTec device
   * Uses limited retry for device operations
   */
  read(): Observable<any> {
    const url = `${this.baseUrl}/read`;
    const headers = new HttpHeaders({ accept: 'text/plain' });
    return this.http.post(url, '', { headers })
      .pipe(
        this.createRetryStrategy(2, 2000, false), // POST to device, limited retries
        this.handleError('read')
      );
  }

  /**
   * Switch browser window for VittalTec integration
   * Uses standard retry logic as it's a simple GET operation
   */
  switchWindowBrowser(): Observable<any> {
    const url = `${this.baseUrl}/switchWindowBrowser`;
    return this.http.get(url, { headers: new HttpHeaders({ accept: '*/*' }) })
      .pipe(
        this.createRetryStrategy(3, 1000, true), // GET is idempotent, safe to retry
        this.handleError('switchWindowBrowser')
      );
  }
  //#endregion

  //#region Metodos api
  /**
   * Register log request for exam integration
   * Uses retry logic for API resilience with moderate retry attempts
   */
  RegistraLogRequisicao(obj: LogRequisicaoExameModelView): Observable<any> {
    const url = `${this.endpointApi}/RegistraLogRequisicao`;
    return this.http.post(url, obj)
      .pipe(
        this.createRetryStrategy(3, 1500, false), // POST with data, moderate retries
        this.handleError('RegistraLogRequisicao')
      );
  }

  /**
   * Register exam data through API
   * Uses retry logic for critical exam data submission
   */
  RegistrarExame(obj: IntegrationApiExameModelView): Observable<any> {
    const url = `${this.endpointApi}/RegistrarExame`;
    return this.http.post(url, obj)
      .pipe(
        this.createRetryStrategy(3, 1500, false), // POST with data, moderate retries
        this.handleError('RegistrarExame')
      );
  }
  //#endregion
} 